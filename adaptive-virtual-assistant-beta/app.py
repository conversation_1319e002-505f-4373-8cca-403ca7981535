# app.py
import os
import logging
from logging.handlers import RotatingFileHandler

from flask import Flask, render_template, request
from flask_login import Login<PERSON>anager

from config import Config, KEYWORDS
from utils.nlp_utils import build_keyword_synonym_dict
from data.data import load_profiles
from models.user import User


# --- Function: create_app ---
def create_app():
    app = Flask(__name__) 
    app.config.from_object(Config) # loads config values from the config class like secret keys and other flask settings

    # Initialize Flask-Login
    login_manager = LoginManager(app)
    login_manager.login_view = 'auth.login' # sets up login handling and tells flask-login what the login route is if user not logged in

    @login_manager.user_loader

# --- Function: load_user ---
    def load_user(user_id):
        # Check if user_id is valid (should be a string)
        if not isinstance(user_id, str):
            app.logger.warning(f'Invalid user_id type: {type(user_id)} - {user_id}')
            return None
        
        profiles = load_profiles() # grabs profiles from json file using your custom loader function
        if not profiles:
            return None

        user_profile = profiles.get(user_id)
        if user_profile:
            # use get to safely access all profile fields
            username = user_profile.get('username')
            if not username:
                app.logger.warning(f'Profile for user_id {user_id} missing required username field')
                return None

            user = User(
                username=username,
                is_admin=user_profile.get('is_admin', False)
            )
            user.password_hash = user_profile.get('password_hash')
            user.name = user_profile.get('name')
            user.user_id = user_profile.get('user_id')
            return user
        return None

    # Set up logging when not in debug mode
    if not app.debug:
        os.makedirs('logs', exist_ok=True)  # creates logs directory if not there already
        file_handler = RotatingFileHandler('logs/app.log', maxBytes=10240, backupCount=10) # sets up log file rotation so app.log doesn’t get too big
        file_handler.setLevel(logging.INFO)
        formatter = logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        )
        file_handler.setFormatter(formatter) # defines how logs will look like with timestamp and file info
        app.logger.addHandler(file_handler)
        app.logger.setLevel(logging.INFO)
        app.logger.info('Application startup') # log that the app started successfully

    # Build the keyword synonyms dictionary at startup
    app.config['KEYWORD_SYNONYMS'] = build_keyword_synonym_dict(KEYWORDS)  # builds and stores the keyword to synonym mappings in config for later use by nlp features

    # Register blueprints
    from routes.main import main as main_blueprint
    app.register_blueprint(main_blueprint)
    app.logger.info('Registered main blueprint')

    from routes.auth import auth as auth_blueprint
    app.register_blueprint(auth_blueprint)
    app.logger.info('Registered auth blueprint')

    from routes.admin import admin as admin_blueprint
    app.register_blueprint(admin_blueprint)
    app.logger.info('Registered admin blueprint')

    # Error handlers
    
    @app.errorhandler(401) # handles unauthorized access like if login required but not provided

# --- Function: unauthorized_error ---
    def unauthorized_error(error):
        app.logger.warning(f'Unauthorized: {request.url}')
        return render_template('errors/401.html'), 401

    @app.errorhandler(403) # handles forbidden access like when user lacks permission

# --- Function: forbidden_error ---
    def forbidden_error(error):
        app.logger.warning(f'Access Denied: {request.url}')
        return render_template('errors/403.html'), 403
    
    @app.errorhandler(404)  # handles not found pages when route or resource doesn't exist

# --- Function: not_found_error ---
    def not_found_error(error):
        app.logger.warning(f'Page not found: {request.url}')
        return render_template('errors/404.html'), 404

    @app.errorhandler(500) # handles internal server errors something went wrong in the code

# --- Function: internal_server_error ---
    def internal_server_error(error):
        app.logger.warning(f'Internal Server Error: {request.url}')
        return render_template('errors/500.html'), 500

    @app.errorhandler(503) # handles service unavailable errors like backend issues

# --- Function: service_unavailable_error ---
    def service_unavailable_error(error):
        app.logger.warning(f'Service Unavailable: {request.url}')
        return render_template('errors/503.html'), 503
    
    # Handle exception
    @app.errorhandler(Exception) # handle missed exception

# --- Function: handle_exception ---
    # Handle exception
    def handle_exception(e):
        logging.error(f"Unhandled Exception: {e}", exc_info=True)
        try:
            return render_template("error.html", error=e), 500
        except Exception:
            # Fallback if error.html template doesn't exist
            return f"<h1>Internal Server Error</h1><p>An unexpected error occurred: {str(e)}</p>", 500

    return app

if __name__ == '__main__':
    app = create_app()
    app.run(debug=False, port = 8080)  # Change 8080 to your desired port